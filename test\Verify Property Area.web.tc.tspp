>>testDataType: undefined
>>summary: 
>>tags: 
>>feature: 
>>ticketUrl: 
>>testCaseId: 
>>importSource: 
>>priority: 
>>aiRequestId: 


--Open URL-- [[http://vibranium.leadrat.info/login?returnUrl=%2Fleads%2Fmanage-leads]] 
--Click on-- {{web > CRM_Login > inpLoginName}} 
--Input value-- [[Jay<PERSON>bad<PERSON>]] --in-- {{web > CRM_Login > inpLoginName}} 
--Click on-- {{web > CRM_Login > inpLoginPassword}} 
--Input value-- [[Test@456]] --in-- {{web > CRM_Login > inpLoginPassword}} 
--Click on-- {{web > CRM_Login > passwordhide}} 
--Click on-- {{web > CRM_Login > Login_1}} 
--Click on-- {{web > CRM_Leads > Add_Lead}} 
--Click on-- {{web > CRM_Leads > inpLeadName}} 
--Input value-- [[Test Property Area]] --in-- {{web > CRM_Leads > inpLeadName}} 
--Click on-- {{web > CRM_Leads > mat_input_0}} 
--Input value-- [[9756783833]] --in-- {{web > CRM_Leads > mat_input_0}} 
--Click on-- {{web > CRM_Leads > mat_input_1}} 
--Input value-- [[8657873764]] --in-- {{web > CRM_Leads > mat_input_1}} 
--Click on-- {{web > CRM_Leads > inpLeadMail}} 
--Input value-- [[<EMAIL>]] --in-- {{web > CRM_Leads > inpLeadMail}} 
--Click on-- {{web > CRM_Leads > inpReferralName}} 
--Input value-- [[Manasa]] --in-- {{web > CRM_Leads > inpReferralName}} 
--Click on-- {{web > CRM_Leads > mat_input_2}} 
--Input value-- [[7647832833]] --in-- {{web > CRM_Leads > mat_input_2}} 
--Click on-- {{web > CRM_Leads > inpReferralMail}} 
--Input value-- [[<EMAIL>]] --in-- {{web > CRM_Leads > inpReferralMail}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > _99_Acres_1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16_17_18}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > test1233_1_2_3_4_5_6_7_8_9_10_11_12_13_14_15}} 
--Click on-- {{web > CRM_Leads > ng_select_focused_ng_select_co}} 
--Click on-- {{web > CRM_Leads > You_1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16}} 
--Click on-- {{web > CRM_Leads > ng_select_focused_ng_select_co}} 
--Click on-- {{web > CRM_Leads > Jay_Vib_Basic_1_2_3_4_5_6}} 
--Click on-- {{web > CRM_Leads > Manually_Enter_Location_1}} 
--Click on-- {{web > CRM_Leads > enter_locality}} 
--Input value-- [[hsr]] --in-- {{web > CRM_Leads > enter_locality}} 
--Click on-- {{web > CRM_Leads > enter_sub_community}} 
--Input value-- [[hsr sub]] --in-- {{web > CRM_Leads > enter_sub_community}} 
--Click on-- {{web > CRM_Leads > enter_community}} 
--Input value-- [[hsr com]] --in-- {{web > CRM_Leads > enter_community}} 
--Click on-- {{web > CRM_Leads > enter_tower_name}} 
--Input value-- [[jk tower]] --in-- {{web > CRM_Leads > enter_tower_name}} 
--Click on-- {{web > CRM_Leads > enter_city}} 
--Input value-- [[bangalore]] --in-- {{web > CRM_Leads > enter_city}} 
--Click on-- {{web > CRM_Leads > enter_state}} 
--Input value-- [[karnataka]] --in-- {{web > CRM_Leads > enter_state}} 
--Click on-- {{web > CRM_Leads > enter_country}} 
--Input value-- [[india]] --in-- {{web > CRM_Leads > enter_country}} 
--Click on-- {{web > CRM_Leads > enter_pincode}} 
--Input value-- [[6789876]] --in-- {{web > CRM_Leads > enter_pincode}} 
--Click on-- {{web > CRM_Leads > inpLeadLowerBudget}} 
--Input value-- [[100]] --in-- {{web > CRM_Leads > inpLeadLowerBudget}} 
--Click on-- {{web > CRM_Leads > inpLeadUpperBudget}} 
--Input value-- [[2000]] --in-- {{web > CRM_Leads > inpLeadUpperBudget}} 
--Click on-- {{web > CRM_Leads > formcontrolname_carpetArea}} 
--Input value-- [[3]] --in-- {{web > CRM_Leads > formcontrolname_carpetArea}} 
--Input value-- [[3000]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > Enquiry_Info_1}} 
--Click on-- {{web > CRM_Leads > Enquiry_Info_1_2}} 
--Click on-- {{web > CRM_Leads > formcontrolname_builtUpArea}} 
--Input value-- [[5]] --in-- {{web > CRM_Leads > formcontrolname_builtUpArea}} 
--Input value-- [[5999]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > formcontrolname_saleableArea}} 
--Input value-- [[5]] --in-- {{web > CRM_Leads > formcontrolname_saleableArea}} 
--Input value-- [[5774]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > formcontrolname_propertyArea}} 
--Input value-- [[3]] --in-- {{web > CRM_Leads > formcontrolname_propertyArea}} 
--Input value-- [[38744]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > Save}} 

