>>testDataType: undefined
>>summary: 
>>tags: 
>>feature: 
>>ticketUrl: 
>>testCaseId: 
>>importSource: 
>>priority: 
>>aiRequestId: 


--Open URL-- [[http://vibranium.leadrat.info/login?returnUrl=%2Fleads%2Fmanage-leads]] 
--Click on-- {{web > CRM_Login > inpLoginName}} 
--Input value-- [[jayvibadmin]] --in-- {{web > CRM_Login > inpLoginName}} 
--Click on-- {{web > CRM_Login > inpLoginPassword}} 
--Input value-- [[Test@456]] --in-- {{web > CRM_Login > inpLoginPassword}} 
--Click on-- {{web > CRM_Login > Login}} 
--Click on-- {{web > CRM_Leads > Add_Lead}} 
--Click on-- {{web > CRM_Leads > inpLeadName}} 
--Input value-- [[test preferred floors]] --in-- {{web > CRM_Leads > inpLeadName}} 
--Click on-- {{web > CRM_Leads > mat_input_0}} 
--Input value-- [[9876567444]] --in-- {{web > CRM_Leads > mat_input_0}} 
--Click on-- {{web > CRM_Leads > mat_input_1}} 
--Input value-- [[8765677474]] --in-- {{web > CRM_Leads > mat_input_1}} 
--Click on-- {{web > CRM_Leads > inpLeadMail}} 
--Input value-- [[<EMAIL>]] --in-- {{web > CRM_Leads > inpLeadMail}} 
--Click on-- {{web > CRM_Leads > inpReferralName}} 
--Input value-- [[manasa]] --in-- {{web > CRM_Leads > inpReferralName}} 
--Click on-- {{web > CRM_Leads > mat_input_2}} 
--Input value-- [[8765673333]] --in-- {{web > CRM_Leads > mat_input_2}} 
--Click on-- {{web > CRM_Leads > inpReferralMail}} 
--Input value-- [[<EMAIL>]] --in-- {{web > CRM_Leads > inpReferralMail}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > _99_Acres_1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16_17_18_19_20_21_22_23_24_25_26_27}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > jayakumar_k_leadrat_com_1_2}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > You_1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16_17_18_19_20_21_22_23_24_25}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > abid_ansari_1_2_3_4_5_6_7_8_9_10_11_12}} 
--Click on-- {{web > CRM_Leads > Manually_Enter_Location_1}} 
--Click on-- {{web > CRM_Leads > enter_locality}} 
--Input value-- [[HSR]] --in-- {{web > CRM_Leads > enter_locality}} 
--Click on-- {{web > CRM_Leads > enter_sub_community}} 
--Input value-- [[HSR SUB]] --in-- {{web > CRM_Leads > enter_sub_community}} 
--Click on-- {{web > CRM_Leads > enter_community}} 
--Input value-- [[HSR COM]] --in-- {{web > CRM_Leads > enter_community}} 
--Click on-- {{web > CRM_Leads > enter_tower_name}} 
--Input value-- [[JK tower]] --in-- {{web > CRM_Leads > enter_tower_name}} 
--Click on-- {{web > CRM_Leads > enter_city}} 
--Input value-- [[Bangalore]] --in-- {{web > CRM_Leads > enter_city}} 
--Click on-- {{web > CRM_Leads > enter_state}} 
--Input value-- [[Karnataka]] --in-- {{web > CRM_Leads > enter_state}} 
--Click on-- {{web > CRM_Leads > enter_country}} 
--Input value-- [[India]] --in-- {{web > CRM_Leads > enter_country}} 
--Click on-- {{web > CRM_Leads > enter_pincode}} 
--Input value-- [[569948]] --in-- {{web > CRM_Leads > enter_pincode}} 
--Click on-- {{web > CRM_Leads > inpLeadLowerBudget}} 
--Click on-- {{web > CRM_Leads > inpLeadLowerBudget}} 
--Input value-- [[100]] --in-- {{web > CRM_Leads > inpLeadLowerBudget}} 
--Click on-- {{web > CRM_Leads > inpLeadUpperBudget}} 
--Input value-- [[4000]] --in-- {{web > CRM_Leads > inpLeadUpperBudget}} 
--Click on-- {{web > CRM_Leads > formcontrolname_carpetArea}} 
--Input value-- [[5]] --in-- {{web > CRM_Leads > formcontrolname_carpetArea}} 
--Input value-- [[500]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > formcontrolname_builtUpArea}} 
--Input value-- [[4]] --in-- {{web > CRM_Leads > formcontrolname_builtUpArea}} 
--Input value-- [[454]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > formcontrolname_saleableArea}} 
--Input value-- [[6]] --in-- {{web > CRM_Leads > formcontrolname_saleableArea}} 
--Input value-- [[6543]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > formcontrolname_propertyArea}} 
--Input value-- [[7]] --in-- {{web > CRM_Leads > formcontrolname_propertyArea}} 
--Input value-- [[765]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > formcontrolname_netArea}} 
--Input value-- [[6]] --in-- {{web > CRM_Leads > formcontrolname_netArea}} 
--Input value-- [[654]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > inpLeadunitNumber}} 
--Input value-- [[sdfg34545]] --in-- {{web > CRM_Leads > inpLeadunitNumber}} 
--Click on-- {{web > CRM_Leads > inpLeadClusterName}} 
--Input value-- [[HSR cluster]] --in-- {{web > CRM_Leads > inpLeadClusterName}} 
--Click on-- {{web > CRM_Leads > Select_purpose}} 
--Click on-- {{web > CRM_Leads > ng_select_focused_ng_select_co}} 
--Click on-- {{web > CRM_Leads > ng_select_focused_ng_select_co}} 
--Click on-- {{web > CRM_Leads > Investment_1_2_3_4_5}} 
--Click on-- {{web > CRM_Leads > Investment_1_2_3_4_5}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > Rent}} 
--Click on-- {{web > CRM_Leads > ng_select_focused_ng_select_co}} 
--Click on-- {{web > CRM_Leads > Residential_1_2_3}} 
--Click on-- {{web > CRM_Leads > ng_select_focused_ng_select_co}} 
--Click on-- {{web > CRM_Leads > Independent_House}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > _1_1}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > Studio_1}} 
--Click on-- {{web > CRM_Leads > Property_Area}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > Upper_Basement}} 
--Click on-- {{web > CRM_Leads > Furnish_Status}} 
--Click on-- {{web > CRM_Leads > Save}} 

