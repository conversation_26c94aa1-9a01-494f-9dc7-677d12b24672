>>testDataType: undefined
>>summary: 
>>tags: 
>>feature: 
>>ticketUrl: 
>>testCaseId: 
>>importSource: 
>>priority: 
>>aiRequestId: 


--Open URL-- [[http://vibranium.leadrat.info/login?returnUrl=%2Fleads%2Fmanage-leads]] 
--Click on-- {{web > CRM_Login > inpLoginName}} 
--Input value-- [[JayVibAdmin]] --in-- {{web > CRM_Login > inpLoginName}} 
--Click on-- {{web > CRM_Login > inpLoginPassword}} 
--Input value-- [[Test@456]] --in-- {{web > CRM_Login > inpLoginPassword}} 
--Click on-- {{web > CRM_Login > passwordhide}} 
--Click on-- {{web > CRM_Login > Login_1}} 
--Click on-- {{web > CRM_Leads > Add_Lead_1}} 
--Click on-- {{web > CRM_Leads > inpLeadName}} 
--Input value-- [[Test Re]] --in-- {{web > CRM_Leads > inpLeadName}} 
--Click on-- {{web > CRM_Leads > inpLeadName}} 
--Input value-- [[Test Referral Email]] --in-- {{web > CRM_Leads > inpLeadName}} 
--Click on-- {{web > CRM_Leads > mat_input_0}} 
--Input value-- [[6789827265]] --in-- {{web > CRM_Leads > mat_input_0}} 
--Click on-- {{web > CRM_Leads > mat_input_1}} 
--Input value-- [[96]] --in-- {{web > CRM_Leads > mat_input_1}} 
--Press keyboard keys-- [[KeyT]] --on-- {{web > CRM_Leads > mat_input_1}} 
--Input value-- [[9667676722]] --in-- {{web > CRM_Leads > mat_input_1}} 
--Click on-- {{web > CRM_Leads > inpLeadMail}} 
--Input value-- [[<EMAIL>]] --in-- {{web > CRM_Leads > inpLeadMail}} 
--Click on-- {{web > CRM_Leads > inpReferralName}} 
--Input value-- [[Manasa]] --in-- {{web > CRM_Leads > inpReferralName}} 
--Click on-- {{web > CRM_Leads > mat_input_2}} 
--Input value-- [[8267878323]] --in-- {{web > CRM_Leads > mat_input_2}} 
--Click on-- {{web > CRM_Leads > inpReferralMail}} 
--Input value-- [[<EMAIL>]] --in-- {{web > CRM_Leads > inpReferralMail}} 
--Click on-- {{web > CRM_Leads > Save}} 

