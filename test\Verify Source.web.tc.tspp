>>testDataType: undefined
>>summary: 
>>tags: 
>>feature: 
>>ticketUrl: 
>>testCaseId: 
>>importSource: 
>>priority: 
>>aiRequestId: 


--Open URL-- [[http://vibranium.leadrat.info/login?returnUrl=%2Fleads%2Fmanage-leads]] 
--Click on-- {{web > CRM_Login > inpLoginName}} 
--Input value-- [[Jay<PERSON>bad<PERSON>]] --in-- {{web > CRM_Login > inpLoginName}} 
--Click on-- {{web > CRM_Login > inpLoginPassword}} 
--Input value-- [[Test@456]] --in-- {{web > CRM_Login > inpLoginPassword}} 
--Click on-- {{web > CRM_Login > Login}} 
--Click on-- {{web > CRM_Leads > Add_Lead}} 
--Click on-- {{web > CRM_Leads > inpLeadName}} 
--Input value-- [[test source]] --in-- {{web > CRM_Leads > inpLeadName}} 
--Click on-- {{web > CRM_Leads > mat_input_0}} 
--Input value-- [[6896767787]] --in-- {{web > CRM_Leads > mat_input_0}} 
--Click on-- {{web > CRM_Leads > mat_input_1}} 
--Input value-- [[6866576776]] --in-- {{web > CRM_Leads > mat_input_1}} 
--Click on-- {{web > CRM_Leads > inpLeadMail}} 
--Input value-- [[<EMAIL>]] --in-- {{web > CRM_Leads > inpLeadMail}} 
--Click on-- {{web > CRM_Leads > inpReferralName}} 
--Input value-- [[Manasa]] --in-- {{web > CRM_Leads > inpReferralName}} 
--Click on-- {{web > CRM_Leads > mat_input_2}} 
--Input value-- [[9767878777]] --in-- {{web > CRM_Leads > mat_input_2}} 
--Click on-- {{web > CRM_Leads > inpReferralMail}} 
--Input value-- [[<EMAIL>]] --in-- {{web > CRM_Leads > inpReferralMail}} 
--Click on-- {{web > CRM_Leads > ng_select_focused_ng_arrow_wra}} 
--Click on-- {{web > CRM_Leads > ng_select_focused_ng_arrow_wra}} 
--Click on-- {{web > CRM_Leads > _99_Acres}} 
--Click on-- {{web > CRM_Leads > Save}} 

