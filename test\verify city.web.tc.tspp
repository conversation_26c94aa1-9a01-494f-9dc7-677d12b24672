>>testDataType: undefined
>>summary: 
>>tags: 
>>feature: 
>>ticketUrl: 
>>testCaseId: 
>>importSource: 
>>priority: 
>>aiRequestId: 


--Open URL-- [[http://vibranium.leadrat.info/login?returnUrl=%2Fleads%2Fmanage-leads]] 
--Click on-- {{web > CRM_Login > inpLoginName}} 
--Input value-- [[Jay<PERSON>bad<PERSON>]] --in-- {{web > CRM_Login > inpLoginName}} 
--Click on-- {{web > CRM_Login > inpLoginPassword}} 
--Input value-- [[Test@456]] --in-- {{web > CRM_Login > inpLoginPassword}} 
--Click on-- {{web > CRM_Login > Login_1}} 
--Click on-- {{web > CRM_Leads > Add_Lead}} 
--Click on-- {{web > CRM_Leads > inpLeadName}} 
--Input value-- [[Test city]] --in-- {{web > CRM_Leads > inpLeadName}} 
--Click on-- {{web > CRM_Leads > mat_input_0}} 
--Input value-- [[8547754378]] --in-- {{web > CRM_Leads > mat_input_0}} 
--Click on-- {{web > CRM_Leads > mat_input_1}} 
--Input value-- [[8363368894]] --in-- {{web > CRM_Leads > mat_input_1}} 
--Click on-- {{web > CRM_Leads > inpLeadMail}} 
--Input value-- [[<EMAIL>]] --in-- {{web > CRM_Leads > inpLeadMail}} 
--Click on-- {{web > CRM_Leads > inpReferralName}} 
--Input value-- [[Manasa]] --in-- {{web > CRM_Leads > inpReferralName}} 
--Click on-- {{web > CRM_Leads > mat_input_2}} 
--Input value-- [[9275738293]] --in-- {{web > CRM_Leads > mat_input_2}} 
--Click on-- {{web > CRM_Leads > inpReferralMail}} 
--Input value-- [[<EMAIL>]] --in-- {{web > CRM_Leads > inpReferralMail}} 
--Click on-- {{web > CRM_Leads > ng_select_focused_ng_arrow_wra}} 
--Click on-- {{web > CRM_Leads > _99_Acres_1_2_3_4_5_6_7_8_9}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > test1233_1_2_3_4_5_6}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > You_1_2_3_4_5_6_7}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > abid_ansari_1_2_3_4_5_6}} 
--Click on-- {{web > CRM_Leads > Manually_Enter_Location_1}} 
--Click on-- {{web > CRM_Leads > enter_locality}} 
--Input value-- [[HSR]] --in-- {{web > CRM_Leads > enter_locality}} 
--Click on-- {{web > CRM_Leads > enter_sub_community}} 
--Input value-- [[HSR SUB]] --in-- {{web > CRM_Leads > enter_sub_community}} 
--Click on-- {{web > CRM_Leads > enter_community}} 
--Input value-- [[HSR COM]] --in-- {{web > CRM_Leads > enter_community}} 
--Click on-- {{web > CRM_Leads > enter_tower_name}} 
--Input value-- [[JK tower]] --in-- {{web > CRM_Leads > enter_tower_name}} 
--Click on-- {{web > CRM_Leads > enter_city}} 
--Input value-- [[Bangalore]] --in-- {{web > CRM_Leads > enter_city}} 
--Click on-- {{web > CRM_Leads > Save}} 

