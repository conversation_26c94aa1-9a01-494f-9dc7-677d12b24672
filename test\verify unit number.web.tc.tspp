>>testDataType: undefined
>>summary: 
>>tags: 
>>feature: 
>>ticketUrl: 
>>testCaseId: 
>>importSource: 
>>priority: 
>>aiRequestId: 


--Open URL-- [[http://vibranium.leadrat.info/login?returnUrl=%2Fleads%2Fmanage-leads]] 
--Click on-- {{web > CRM_Login > inpLoginName}} 
--Input value-- [[Jay<PERSON>bad<PERSON>]] --in-- {{web > CRM_Login > inpLoginName}} 
--Click on-- {{web > CRM_Login > inpLoginPassword}} 
--Input value-- [[Test@456]] --in-- {{web > CRM_Login > inpLoginPassword}} 
--Click on-- {{web > CRM_Login > Login_1}} 
--Click on-- {{web > CRM_Leads > Add_Lead_1}} 
--Click on-- {{web > CRM_Leads > inpLeadName}} 
--Press keyboard keys-- [[Shift]] --on-- {{web > CRM_Leads > inpLeadName}} 
--Input value-- [[test unit]] --in-- {{web > CRM_Leads > inpLeadName}} 
--Click on-- {{web > CRM_Leads > mat_input_0}} 
--Input value-- [[9978293847]] --in-- {{web > CRM_Leads > mat_input_0}} 
--Click on-- {{web > CRM_Leads > mat_input_1}} 
--Input value-- [[9728284944]] --in-- {{web > CRM_Leads > mat_input_1}} 
--Click on-- {{web > CRM_Leads > inpLeadMail}} 
--Input value-- [[<EMAIL>]] --in-- {{web > CRM_Leads > inpLeadMail}} 
--Click on-- {{web > CRM_Leads > inpReferralName}} 
--Input value-- [[Manasa]] --in-- {{web > CRM_Leads > inpReferralName}} 
--Click on-- {{web > CRM_Leads > mat_input_2}} 
--Input value-- [[8478383644]] --in-- {{web > CRM_Leads > mat_input_2}} 
--Click on-- {{web > CRM_Leads > inpReferralMail}} 
--Input value-- [[<EMAIL>]] --in-- {{web > CRM_Leads > inpReferralMail}} 
--Click on-- {{web > CRM_Leads > ng_select_focused_ng_arrow_wra}} 
--Click on-- {{web > CRM_Leads > _99_Acres_1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16_17_18_19_20}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > test1233_1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16_17}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > You_1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16_17_18}} 
--Click on-- {{web > CRM_Leads > aria_activedescendant}} 
--Click on-- {{web > CRM_Leads > Jay_Vib_Basic_1_2_3_4_5_6_7}} 
--Click on-- {{web > CRM_Leads > Manually_Enter_Location_1}} 
--Click on-- {{web > CRM_Leads > enter_locality}} 
--Input value-- [[HSR]] --in-- {{web > CRM_Leads > enter_locality}} 
--Click on-- {{web > CRM_Leads > enter_sub_community}} 
--Input value-- [[HSR SUB]] --in-- {{web > CRM_Leads > enter_sub_community}} 
--Click on-- {{web > CRM_Leads > enter_community}} 
--Input value-- [[HSR COM]] --in-- {{web > CRM_Leads > enter_community}} 
--Click on-- {{web > CRM_Leads > enter_tower_name}} 
--Input value-- [[JK tower]] --in-- {{web > CRM_Leads > enter_tower_name}} 
--Click on-- {{web > CRM_Leads > enter_city}} 
--Input value-- [[Bangalore]] --in-- {{web > CRM_Leads > enter_city}} 
--Click on-- {{web > CRM_Leads > enter_state}} 
--Input value-- [[Karnataka]] --in-- {{web > CRM_Leads > enter_state}} 
--Click on-- {{web > CRM_Leads > enter_country}} 
--Input value-- [[India]] --in-- {{web > CRM_Leads > enter_country}} 
--Click on-- {{web > CRM_Leads > enter_pincode}} 
--Input value-- [[560098]] --in-- {{web > CRM_Leads > enter_pincode}} 
--Click on-- {{web > CRM_Leads > inpLeadLowerBudget}} 
--Input value-- [[2345]] --in-- {{web > CRM_Leads > inpLeadLowerBudget}} 
--Click on-- {{web > CRM_Leads > inpLeadUpperBudget}} 
--Input value-- [[4564]] --in-- {{web > CRM_Leads > inpLeadUpperBudget}} 
--Click on-- {{web > CRM_Leads > formcontrolname_carpetArea}} 
--Input value-- [[4]] --in-- {{web > CRM_Leads > formcontrolname_carpetArea}} 
--Input value-- [[45]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > formcontrolname_builtUpArea}} 
--Input value-- [[5]] --in-- {{web > CRM_Leads > formcontrolname_builtUpArea}} 
--Input value-- [[56]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > formcontrolname_saleableArea}} 
--Input value-- [[6]] --in-- {{web > CRM_Leads > formcontrolname_saleableArea}} 
--Input value-- [[67]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > formcontrolname_propertyArea}} 
--Click on-- {{web > CRM_Leads > formcontrolname_propertyArea}} 
--Input value-- [[5]] --in-- {{web > CRM_Leads > formcontrolname_propertyArea}} 
--Input value-- [[54]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > formcontrolname_netArea}} 
--Input value-- [[4]] --in-- {{web > CRM_Leads > formcontrolname_netArea}} 
--Input value-- [[45]] --in-- {{web > CRM_Leads > ng_untouched_ng_dirty}} 
--Click on-- {{web > CRM_Leads > inpLeadunitNumber}} 
--Input value-- [[ABCD1234]] --in-- {{web > CRM_Leads > inpLeadunitNumber}} 
--Click on-- {{web > CRM_Leads > Save}} 

