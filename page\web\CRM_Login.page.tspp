>>summary: 
>>recorderName: CRM|Login

==Username;CSS;form;;[XPATH://login-form//form];[Username];[CRM|Login];;==
==inpLoginName;CSS;#inpLoginName;Input;[CSS:[placeholder='Enter your username']^^CSS:[type='text']^^XPATH://form-errors-wrapper[@label="Username"]//input[@id="inpLoginName"]];[inpLoginName];[CRM|Login];;==
==inpLoginPassword;CSS;#inpLoginPassword;Input;[CSS:[placeholder='Enter your password']^^CSS:[type='password']^^CSS:.ng-untouched^^CSS:.ng-pristine^^XPATH://form-errors-wrapper[@label="Password"]//input[@id="inpLoginPassword"]];[inpLoginPassword];[CRM|Login];;==
==Login;XPATH;//span[text()="Login"];;[CSS:.btn-accent-green-xl span^^XPATH://h4[contains(@class, 'btn-accent-green-xl')]//span[text()="Login"]];[Login];[CRM|Login];;==
==passwordhide;CSS;#passwordhide;;[CSS:.ic-gray^^CSS:.ic-eye-solid^^XPATH://form-errors-wrapper[@label="Password"]//a[@id="passwordhide"]];[passwordhide];[CRM|Login];;==
==Login_1;CSS;.btn-accent-green-xl;;[XPATH://login-form//h4[contains(@class, 'btn-accent-green-xl')]];[Login];[CRM|Login];;==
==Forgot_Password;CSS;.justify-end;;[XPATH://login-form//div[contains(@class, 'justify-end')]];[Forgot_Password];[CRM|Login];;==







